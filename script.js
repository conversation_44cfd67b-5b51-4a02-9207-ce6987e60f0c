// JavaScript 代码
document.addEventListener('DOMContentLoaded', function() {
    const button = document.getElementById('myButton');
    
    button.addEventListener('click', function() {
        alert('您好！按钮被点击了！');
        
        // 改变按钮文本
        if (button.textContent === '点击我') {
            button.textContent = '再次点击';
        } else {
            button.textContent = '点击我';
        }
        
        // 添加一些动画效果
        button.style.transform = 'scale(0.95)';
        setTimeout(() => {
            button.style.transform = 'scale(1)';
        }, 150);
    });
});

// 控制台输出欢迎信息
console.log('JavaScript 已加载！欢迎来到您的项目！'); 